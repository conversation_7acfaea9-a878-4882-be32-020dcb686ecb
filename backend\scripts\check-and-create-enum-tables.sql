-- ============================================================================
-- CHECK AND CREATE ENUM TABLES
-- ============================================================================
-- This script checks what tables exist and creates missing enum tables
-- Run this BEFORE running remove-bilingual-system.sql

-- STEP 1: CHECK EXISTING TABLES
-- ============================================================================

-- Check what tables currently exist
SELECT 'Existing tables in database:' as info;
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_type = 'BASE TABLE'
ORDER BY table_name;

-- STEP 2: CREATE ENUM TABLES IF THEY DON'T EXIST
-- ============================================================================

-- Create departments_enum table
CREATE TABLE IF NOT EXISTS departments_enum (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name_en VARCHAR(255),
    name_vi VARCHAR(255),
    description_en TEXT,
    description_vi TEXT,
    color_code VARCHAR(7),
    icon_name VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create specialties table
CREATE TABLE IF NOT EXISTS specialties (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name_en VARCHAR(255),
    name_vi VARCHAR(255),
    description_en TEXT,
    description_vi TEXT,
    color_code VARCHAR(7),
    icon_name VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create room_types table
CREATE TABLE IF NOT EXISTS room_types (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name_en VARCHAR(255),
    name_vi VARCHAR(255),
    description_en TEXT,
    description_vi TEXT,
    color_code VARCHAR(7),
    icon_name VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create diagnosis table
CREATE TABLE IF NOT EXISTS diagnosis (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name_en VARCHAR(255),
    name_vi VARCHAR(255),
    description_en TEXT,
    description_vi TEXT,
    color_code VARCHAR(7),
    icon_name VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    icd_code VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create medications table
CREATE TABLE IF NOT EXISTS medications (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name_en VARCHAR(255),
    name_vi VARCHAR(255),
    description_en TEXT,
    description_vi TEXT,
    color_code VARCHAR(7),
    icon_name VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    dosage_form VARCHAR(100),
    strength VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create status_values table
CREATE TABLE IF NOT EXISTS status_values (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name_en VARCHAR(255),
    name_vi VARCHAR(255),
    description_en TEXT,
    description_vi TEXT,
    color_code VARCHAR(7),
    icon_name VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    applies_to VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create payment_methods table
CREATE TABLE IF NOT EXISTS payment_methods (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name_en VARCHAR(255),
    name_vi VARCHAR(255),
    description_en TEXT,
    description_vi TEXT,
    color_code VARCHAR(7),
    icon_name VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    requires_verification BOOLEAN DEFAULT false,
    processing_fee DECIMAL(8,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- STEP 3: VERIFY TABLES WERE CREATED
-- ============================================================================

SELECT 'Enum tables after creation:' as info;
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
    'departments_enum', 'specialties', 'room_types', 
    'diagnosis', 'medications', 'status_values', 'payment_methods'
)
ORDER BY table_name;

-- Check if doctors table exists and has languages_spoken column
SELECT 'Checking doctors table structure:' as info;
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'doctors' 
AND column_name IN ('languages_spoken', 'doctor_id', 'profile_id')
ORDER BY column_name;

SELECT 'Setup completed! You can now run remove-bilingual-system.sql' as message;
