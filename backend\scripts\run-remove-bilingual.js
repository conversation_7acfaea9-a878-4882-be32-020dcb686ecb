#!/usr/bin/env node

/**
 * Remove Bilingual System Script
 * 
 * This script removes all bilingual features from the hospital management system
 * and converts it to Vietnamese-only.
 * 
 * Usage:
 * NEXT_PUBLIC_SUPABASE_URL=your-url SUPABASE_SERVICE_ROLE_KEY=your-key node run-remove-bilingual.js
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables:');
  console.error('   NEXT_PUBLIC_SUPABASE_URL');
  console.error('   SUPABASE_SERVICE_ROLE_KEY');
  console.error('\nUsage:');
  console.error('NEXT_PUBLIC_SUPABASE_URL=your-url SUPABASE_SERVICE_ROLE_KEY=your-key node run-remove-bilingual.js');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runSQLFile(filePath, description) {
  console.log(`\n🔄 ${description}...`);
  
  try {
    const sqlContent = fs.readFileSync(filePath, 'utf8');
    
    // Split SQL content by semicolons and execute each statement
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    for (const statement of statements) {
      if (statement.trim()) {
        const { error } = await supabase.rpc('exec_sql', { sql_query: statement });
        
        if (error) {
          console.error(`❌ Error executing SQL: ${error.message}`);
          console.error(`Statement: ${statement.substring(0, 100)}...`);
        }
      }
    }
    
    console.log(`✅ ${description} completed successfully!`);
    
  } catch (error) {
    console.error(`❌ Error reading SQL file: ${error.message}`);
    console.error('\n💡 Manual execution required:');
    console.error(`   1. Open Supabase SQL Editor`);
    console.error(`   2. Copy and paste content from: ${filePath}`);
    console.error(`   3. Execute the SQL commands`);
  }
}

async function checkDatabaseChanges() {
  console.log('\n🔍 Verifying database changes...');
  
  try {
    // Check if languages_spoken column is removed from doctors table
    const { data: doctorColumns, error: doctorError } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'doctors')
      .eq('column_name', 'languages_spoken');
    
    if (doctorError) {
      console.log('   ⚠️  Could not verify doctors table structure');
    } else if (doctorColumns && doctorColumns.length === 0) {
      console.log('   ✅ languages_spoken column removed from doctors table');
    } else {
      console.log('   ❌ languages_spoken column still exists in doctors table');
    }
    
    // Check enum tables structure
    const enumTables = ['departments_enum', 'specialties', 'room_types'];
    
    for (const tableName of enumTables) {
      const { data: columns, error } = await supabase
        .from('information_schema.columns')
        .select('column_name')
        .eq('table_name', tableName)
        .in('column_name', ['name_en', 'description_en']);
      
      if (error) {
        console.log(`   ⚠️  Could not verify ${tableName} table structure`);
      } else if (columns && columns.length === 0) {
        console.log(`   ✅ ${tableName} converted to Vietnamese-only`);
      } else {
        console.log(`   ❌ ${tableName} still has English columns`);
      }
    }
    
    // Show sample data
    console.log('\n📊 Sample data verification:');
    
    const { data: sampleDepts, error: deptError } = await supabase
      .from('departments_enum')
      .select('code, name, description')
      .limit(2);
    
    if (!deptError && sampleDepts) {
      console.log('   departments_enum sample:');
      sampleDepts.forEach(dept => {
        console.log(`     ${dept.code}: ${dept.name}`);
      });
    }
    
  } catch (error) {
    console.error(`❌ Error verifying changes: ${error.message}`);
  }
}

async function main() {
  console.log('🚀 Starting bilingual system removal...');
  console.log('=====================================');
  
  try {
    // Step 1: Remove bilingual system
    const removeBilingualPath = path.join(__dirname, 'remove-bilingual-system.sql');
    await runSQLFile(removeBilingualPath, 'Removing bilingual system');
    
    // Step 2: Populate with Vietnamese-only data
    const populateVietnamPath = path.join(__dirname, 'populate-enum-tables-vietnam-only.sql');
    await runSQLFile(populateVietnamPath, 'Populating Vietnamese-only data');
    
    // Step 3: Verify changes
    await checkDatabaseChanges();
    
    console.log('\n🎉 Bilingual system removal completed!');
    console.log('=====================================');
    console.log('✅ All bilingual features have been removed');
    console.log('✅ Database converted to Vietnamese-only');
    console.log('✅ Enum tables updated with Vietnamese data');
    console.log('\n📝 Next steps:');
    console.log('   1. Update frontend components to use Vietnamese-only data');
    console.log('   2. Remove any remaining translation references in code');
    console.log('   3. Test the application to ensure everything works correctly');
    
  } catch (error) {
    console.error(`❌ Fatal error: ${error.message}`);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n⚠️  Process interrupted by user');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n⚠️  Process terminated');
  process.exit(0);
});

// Run the main function
main().catch(error => {
  console.error('❌ Unhandled error:', error);
  process.exit(1);
});
