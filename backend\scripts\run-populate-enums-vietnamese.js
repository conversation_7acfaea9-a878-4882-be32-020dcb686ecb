#!/usr/bin/env node

/**
 * Script để populate enum tables với dữ liệu tiếng Việt
 * Phiên bản đơn giản chỉ sử dụng tiếng Việt
 */

const fs = require('fs');
const path = require('path');

console.log('🇻🇳 HƯỚNG DẪN POPULATE ENUM TABLES - VIETNAMESE ONLY');
console.log('=====================================================');
console.log('');

console.log('📋 Các bước thực hiện:');
console.log('');
console.log('1️⃣  Mở Supabase Dashboard của bạn');
console.log('2️⃣  Vào SQL Editor');
console.log('3️⃣  Copy toàn bộ nội dung từ file: backend/scripts/populate-enum-tables-vietnamese.sql');
console.log('4️⃣  Paste và chạy SQL script');
console.log('');

// Đọc file SQL
const sqlFilePath = path.join(__dirname, 'populate-enum-tables-vietnamese.sql');

try {
  const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
  
  console.log('📄 File SQL đã sẵn sàng:');
  console.log(`   📍 Đường dẫn: ${sqlFilePath}`);
  console.log(`   📊 Kích thước: ${(sqlContent.length / 1024).toFixed(1)} KB`);
  console.log(`   📝 Số dòng: ${sqlContent.split('\n').length} dòng`);
  console.log('');
  
  console.log('🎯 Script này sẽ populate:');
  console.log('   ✅ departments_enum (10 khoa/phòng ban)');
  console.log('   ✅ specialties (12 chuyên khoa)');
  console.log('   ✅ room_types (8 loại phòng)');
  console.log('   ✅ diagnosis (10 chẩn đoán phổ biến)');
  console.log('   ✅ medications (10 loại thuốc)');
  console.log('   ✅ status_values (10 trạng thái)');
  console.log('   ✅ payment_methods (8 phương thức thanh toán)');
  console.log('');
  
  console.log('🔧 Tính năng:');
  console.log('   🇻🇳 Chỉ sử dụng tiếng Việt');
  console.log('   🎨 Có màu sắc và icon');
  console.log('   📊 Sắp xếp theo thứ tự');
  console.log('   🔄 Cập nhật an toàn (ON CONFLICT)');
  console.log('');
  
  console.log('💡 Sau khi chạy script:');
  console.log('   1. Kiểm tra database: node scripts/check-database-status.js');
  console.log('   2. Test frontend enum data fetching');
  console.log('   3. Refresh ứng dụng để thấy dữ liệu mới');
  console.log('');
  
  console.log('🚀 Sẵn sàng chạy script!');
  
} catch (err) {
  console.error('❌ Lỗi đọc file SQL:', err.message);
  console.log('');
  console.log('🔧 Kiểm tra:');
  console.log('   - File populate-enum-tables-vietnamese.sql có tồn tại không?');
  console.log('   - Đường dẫn có đúng không?');
}

console.log('');
console.log('📞 Cần hỗ trợ? Hãy kiểm tra:');
console.log('   - Supabase connection');
console.log('   - Database permissions');
console.log('   - SQL syntax');
