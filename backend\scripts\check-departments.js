#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
const path = require('path');

require('dotenv').config({ path: path.join(__dirname, '../../.env.local') });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkDepartments() {
  console.log('🔍 Checking departments table...\n');
  
  try {
    const { data, error } = await supabase
      .from('departments')
      .select('*');
    
    if (error) {
      console.error('❌ Error:', error.message);
      return;
    }
    
    console.log('✅ Departments table accessible');
    console.log(`📊 Total rows: ${data?.length || 0}`);
    
    if (data && data.length > 0) {
      console.log('\n🔍 Table structure:');
      Object.keys(data[0]).forEach(key => {
        console.log(`   - ${key}: ${typeof data[0][key]}`);
      });
      
      console.log('\n📋 Sample data:');
      data.slice(0, 3).forEach((dept, index) => {
        console.log(`   ${index + 1}. ${dept.department_id || dept.id}: ${dept.name || dept.department_name}`);
      });
    }
    
  } catch (err) {
    console.error('❌ Error:', err.message);
  }
}

checkDepartments();
