-- ============================================================================
-- REMOVE BILINGUAL SYSTEM FROM HOSPITAL MANAGEMENT DATABASE
-- ============================================================================
-- This script removes all bilingual features and converts to Vietnamese-only
-- Run this in Supabase SQL Editor
-- IMPORTANT: Run check-and-create-enum-tables.sql FIRST if you get table not found errors

-- STEP 1: CHECK EXISTING TABLES
-- ============================================================================

-- Check what tables exist
DO $$
BEGIN
    RAISE NOTICE 'Checking existing tables...';

    -- Check if doctors table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'doctors') THEN
        RAISE NOTICE '✅ doctors table exists';
    ELSE
        RAISE NOTICE '❌ doctors table does not exist';
    END IF;

    -- Check enum tables
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'departments_enum') THEN
        RAISE NOTICE '✅ departments_enum table exists';
    ELSE
        RAISE NOTICE '❌ departments_enum table does not exist - run check-and-create-enum-tables.sql first';
    END IF;
END $$;

-- STEP 2: REMOVE LANGUAGES_SPOKEN COLUMN FROM DOCTORS TABLE
-- ============================================================================

-- Drop the languages_spoken column from doctors table (only if table exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'doctors') THEN
        ALTER TABLE doctors DROP COLUMN IF EXISTS languages_spoken;
        RAISE NOTICE '✅ Removed languages_spoken column from doctors table';
    ELSE
        RAISE NOTICE '⚠️ doctors table does not exist, skipping column removal';
    END IF;
END $$;

-- STEP 3: UPDATE ENUM TABLES TO VIETNAMESE-ONLY
-- ============================================================================

-- Update departments_enum table structure (only if table exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'departments_enum') THEN
        ALTER TABLE departments_enum DROP COLUMN IF EXISTS name_en;
        ALTER TABLE departments_enum DROP COLUMN IF EXISTS description_en;
        RAISE NOTICE '✅ Removed English columns from departments_enum';
    ELSE
        RAISE NOTICE '⚠️ departments_enum table does not exist, skipping';
    END IF;
END $$;

-- Rename Vietnamese columns to be the primary columns
DO $$
BEGIN
    -- Check if name_vi exists and name doesn't exist
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'departments_enum' AND column_name = 'name_vi')
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'departments_enum' AND column_name = 'name') THEN
        ALTER TABLE departments_enum RENAME COLUMN name_vi TO name;
    END IF;
    
    -- Check if description_vi exists and description doesn't exist
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'departments_enum' AND column_name = 'description_vi')
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'departments_enum' AND column_name = 'description') THEN
        ALTER TABLE departments_enum RENAME COLUMN description_vi TO description;
    END IF;
END $$;

-- Update specialties table structure
ALTER TABLE specialties DROP COLUMN IF EXISTS name_en;
ALTER TABLE specialties DROP COLUMN IF EXISTS description_en;

DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'specialties' AND column_name = 'name_vi')
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'specialties' AND column_name = 'name') THEN
        ALTER TABLE specialties RENAME COLUMN name_vi TO name;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'specialties' AND column_name = 'description_vi')
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'specialties' AND column_name = 'description') THEN
        ALTER TABLE specialties RENAME COLUMN description_vi TO description;
    END IF;
END $$;

-- Update room_types table structure
ALTER TABLE room_types DROP COLUMN IF EXISTS name_en;
ALTER TABLE room_types DROP COLUMN IF EXISTS description_en;

DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'room_types' AND column_name = 'name_vi')
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'room_types' AND column_name = 'name') THEN
        ALTER TABLE room_types RENAME COLUMN name_vi TO name;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'room_types' AND column_name = 'description_vi')
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'room_types' AND column_name = 'description') THEN
        ALTER TABLE room_types RENAME COLUMN description_vi TO description;
    END IF;
END $$;

-- Update diagnosis table structure
ALTER TABLE diagnosis DROP COLUMN IF EXISTS name_en;
ALTER TABLE diagnosis DROP COLUMN IF EXISTS description_en;

DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'diagnosis' AND column_name = 'name_vi')
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'diagnosis' AND column_name = 'name') THEN
        ALTER TABLE diagnosis RENAME COLUMN name_vi TO name;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'diagnosis' AND column_name = 'description_vi')
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'diagnosis' AND column_name = 'description') THEN
        ALTER TABLE diagnosis RENAME COLUMN description_vi TO description;
    END IF;
END $$;

-- Update medications table structure
ALTER TABLE medications DROP COLUMN IF EXISTS name_en;
ALTER TABLE medications DROP COLUMN IF EXISTS description_en;

DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'medications' AND column_name = 'name_vi')
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'medications' AND column_name = 'name') THEN
        ALTER TABLE medications RENAME COLUMN name_vi TO name;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'medications' AND column_name = 'description_vi')
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'medications' AND column_name = 'description') THEN
        ALTER TABLE medications RENAME COLUMN description_vi TO description;
    END IF;
END $$;

-- Update status_values table structure
ALTER TABLE status_values DROP COLUMN IF EXISTS name_en;
ALTER TABLE status_values DROP COLUMN IF EXISTS description_en;

DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'status_values' AND column_name = 'name_vi')
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'status_values' AND column_name = 'name') THEN
        ALTER TABLE status_values RENAME COLUMN name_vi TO name;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'status_values' AND column_name = 'description_vi')
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'status_values' AND column_name = 'description') THEN
        ALTER TABLE status_values RENAME COLUMN description_vi TO description;
    END IF;
END $$;

-- Update payment_methods table structure
ALTER TABLE payment_methods DROP COLUMN IF EXISTS name_en;
ALTER TABLE payment_methods DROP COLUMN IF EXISTS description_en;

DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'payment_methods' AND column_name = 'name_vi')
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'payment_methods' AND column_name = 'name') THEN
        ALTER TABLE payment_methods RENAME COLUMN name_vi TO name;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'payment_methods' AND column_name = 'description_vi')
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'payment_methods' AND column_name = 'description') THEN
        ALTER TABLE payment_methods RENAME COLUMN description_vi TO description;
    END IF;
END $$;

-- STEP 3: VERIFICATION
-- ============================================================================

-- Verify the changes
SELECT 'doctors table columns:' as info;
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'doctors' 
ORDER BY ordinal_position;

SELECT 'departments_enum table columns:' as info;
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'departments_enum' 
ORDER BY ordinal_position;

-- Show sample data to verify Vietnamese-only structure
SELECT 'Sample departments_enum data:' as info;
SELECT code, name, description FROM departments_enum LIMIT 3;

SELECT 'Sample specialties data:' as info;
SELECT code, name, description FROM specialties LIMIT 3;
