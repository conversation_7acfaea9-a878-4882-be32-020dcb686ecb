#!/usr/bin/env node

/**
 * <PERSON>ript to populate enum tables with sample data
 * This script reads the populate-enum-tables.sql file and executes it
 */

const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../../.env.local') });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   NEXT_PUBLIC_SUPABASE_URL');
  console.error('   SUPABASE_SERVICE_ROLE_KEY');
  console.error('');
  console.error('Please check your .env.local file in the project root.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function populateEnumTables() {
  try {
    console.log('🔄 Starting enum tables population...');
    console.log(`📡 Connecting to: ${supabaseUrl}`);
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'populate-enum-tables.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    console.log('📄 Loaded SQL file: populate-enum-tables.sql');
    
    // Execute the SQL
    console.log('⚡ Executing SQL commands...');
    const { data, error } = await supabase.rpc('exec_sql', { sql: sqlContent });
    
    if (error) {
      console.error('❌ Error executing SQL:', error);
      process.exit(1);
    }
    
    console.log('✅ SQL executed successfully!');
    
    // Verify the data was inserted
    console.log('🔍 Verifying enum tables...');
    
    const tables = [
      'departments_enum',
      'specialties', 
      'room_types',
      'diagnosis',
      'medications',
      'status_values',
      'payment_methods'
    ];
    
    for (const table of tables) {
      const { data: tableData, error: tableError } = await supabase
        .from(table)
        .select('id, code, name_en, name_vi')
        .eq('is_active', true)
        .order('sort_order');
        
      if (tableError) {
        console.error(`❌ Error checking ${table}:`, tableError);
      } else {
        console.log(`✅ ${table}: ${tableData?.length || 0} records`);
        if (tableData && tableData.length > 0) {
          console.log(`   Sample: ${tableData[0].code} - ${tableData[0].name_en} / ${tableData[0].name_vi}`);
        }
      }
    }
    
    console.log('');
    console.log('🎉 Enum tables population completed successfully!');
    console.log('💡 You can now test your frontend enum data fetching.');
    
  } catch (err) {
    console.error('❌ Unexpected error:', err);
    process.exit(1);
  }
}

// Alternative method using direct SQL execution
async function populateEnumTablesDirectSQL() {
  try {
    console.log('🔄 Starting enum tables population (Direct SQL method)...');
    console.log(`📡 Connecting to: ${supabaseUrl}`);
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'populate-enum-tables.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    console.log('📄 Loaded SQL file: populate-enum-tables.sql');
    console.log('');
    console.log('⚠️  MANUAL EXECUTION REQUIRED:');
    console.log('   1. Open your Supabase Dashboard');
    console.log('   2. Go to SQL Editor');
    console.log('   3. Copy and paste the content from: backend/scripts/populate-enum-tables.sql');
    console.log('   4. Run the SQL script');
    console.log('');
    console.log('📋 SQL file location:', sqlFilePath);
    
  } catch (err) {
    console.error('❌ Error reading SQL file:', err);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  // Try direct SQL execution first, fallback to manual instructions
  populateEnumTablesDirectSQL();
}

module.exports = { populateEnumTables, populateEnumTablesDirectSQL };
