# Hướng dẫn loại bỏ hệ thống Bilingual

## 📋 Tổng quan

Tài liệu này hướng dẫn cách loại bỏ hoàn toàn hệ thống bilingual (song ngữ) khỏi hệ thống quản lý bệnh viện và chuyển đổi sang hệ thống chỉ sử dụng tiếng Việt.

## 🎯 Mục tiêu

- ✅ Loại bỏ trường `languages_spoken` khỏi bảng `doctors`
- ✅ Chuyển đổi tất cả enum tables sang Vietnamese-only
- ✅ Xóa translation service và các file liên quan
- ✅ Cập nhật validators và types
- ✅ Đảm bảo hệ thống hoạt động ổn định với tiếng Việt

## 🛠️ Các bước thực hiện

### Bước 1: Backup dữ liệu (Khuyến nghị)

Trước khi thực hiện, hãy backup dữ liệu quan trọng:

```bash
# Tạo backup
cd backend/scripts
node backup-database.js
```

### Bước 2: Chạy script loại bỏ bilingual

#### Phương pháp 1: Sử dụng script tự động (Khuyến nghị)

```bash
cd backend/scripts

# Thiết lập environment variables
export NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
export SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# Chạy script
node run-remove-bilingual.js
```

#### Phương pháp 2: Thực hiện thủ công

Nếu script tự động không hoạt động, thực hiện thủ công:

1. **Mở Supabase Dashboard**
2. **Vào SQL Editor**
3. **Chạy file SQL đầu tiên:**
   - Copy nội dung từ `backend/scripts/remove-bilingual-system.sql`
   - Paste vào SQL Editor và chạy

4. **Chạy file SQL thứ hai:**
   - Copy nội dung từ `backend/scripts/populate-enum-tables-vietnam-only.sql`
   - Paste vào SQL Editor và chạy

### Bước 3: Kiểm tra kết quả

Sau khi chạy script, kiểm tra:

```sql
-- Kiểm tra cấu trúc bảng doctors
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'doctors' 
ORDER BY ordinal_position;

-- Kiểm tra cấu trúc enum tables
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'departments_enum' 
ORDER BY ordinal_position;

-- Kiểm tra dữ liệu mẫu
SELECT code, name, description FROM departments_enum LIMIT 3;
SELECT code, name, description FROM specialties LIMIT 3;
```

## 📊 Thay đổi trong Database

### Bảng `doctors`
- ❌ **Đã xóa:** `languages_spoken` column

### Enum Tables
Tất cả các bảng enum đã được chuyển đổi:

| Bảng | Trước | Sau |
|------|-------|-----|
| `departments_enum` | `name_en`, `name_vi`, `description_en`, `description_vi` | `name`, `description` |
| `specialties` | `name_en`, `name_vi`, `description_en`, `description_vi` | `name`, `description` |
| `room_types` | `name_en`, `name_vi`, `description_en`, `description_vi` | `name`, `description` |
| `diagnosis` | `name_en`, `name_vi`, `description_en`, `description_vi` | `name`, `description` |
| `medications` | `name_en`, `name_vi`, `description_en`, `description_vi` | `name`, `description` |
| `status_values` | `name_en`, `name_vi`, `description_en`, `description_vi` | `name`, `description` |
| `payment_methods` | `name_en`, `name_vi`, `description_en`, `description_vi` | `name`, `description` |

## 🗂️ Thay đổi trong Code

### Files đã xóa:
- ❌ `backend/shared/src/services/translation.service.ts`
- ❌ `backend/examples/bilingual-demo.ts`

### Files đã cập nhật:
- ✅ `backend/shared/src/validators/all-tables.validators.ts` - Xóa validation cho `languages_spoken`
- ✅ `backend/services/doctor-service/src/validators/doctor.validators.ts` - Xóa validation cho `languages_spoken`

## 🔍 Kiểm tra và Test

### 1. Kiểm tra Database
```bash
cd backend/scripts
node check-database-status.js
```

### 2. Kiểm tra Frontend
- Đảm bảo các component không còn tham chiếu đến `languages_spoken`
- Kiểm tra enum data hiển thị đúng tiếng Việt
- Test các form đăng ký/chỉnh sửa doctor

### 3. Kiểm tra API
- Test API endpoints liên quan đến doctors
- Đảm bảo validation hoạt động đúng
- Kiểm tra enum endpoints

## ⚠️ Lưu ý quan trọng

1. **Backup:** Luôn backup trước khi thực hiện thay đổi
2. **Testing:** Test kỹ lưỡng sau khi thay đổi
3. **Dependencies:** Kiểm tra các dependencies có thể bị ảnh hưởng
4. **Frontend:** Cập nhật frontend components nếu cần

## 🚨 Troubleshooting

### Lỗi thường gặp:

#### 1. "Column languages_spoken does not exist"
```sql
-- Kiểm tra xem column có tồn tại không
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'doctors' AND column_name = 'languages_spoken';
```

#### 2. "Table does not exist"
```sql
-- Kiểm tra các enum tables
SELECT table_name FROM information_schema.tables 
WHERE table_name IN ('departments_enum', 'specialties', 'room_types');
```

#### 3. Permission errors
- Đảm bảo sử dụng `SUPABASE_SERVICE_ROLE_KEY` chứ không phải `SUPABASE_ANON_KEY`
- Kiểm tra quyền truy cập Supabase

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra logs trong console
2. Xem lại các bước thực hiện
3. Kiểm tra environment variables
4. Thử thực hiện thủ công qua Supabase SQL Editor

## ✅ Checklist hoàn thành

- [ ] Backup dữ liệu
- [ ] Chạy script remove-bilingual-system.sql
- [ ] Chạy script populate-enum-tables-vietnam-only.sql
- [ ] Kiểm tra cấu trúc database
- [ ] Test frontend components
- [ ] Test API endpoints
- [ ] Xác nhận hệ thống hoạt động ổn định
