#!/usr/bin/env node

/**
 * Script để populate bảng departments với dữ liệu tiếng Việt
 * Sử dụng bảng departments thay vì departments_enum
 */

const fs = require('fs');
const path = require('path');

console.log('🏥 HƯỚNG DẪN POPULATE BẢNG DEPARTMENTS');
console.log('=====================================================');
console.log('');

console.log('📋 Các bước thực hiện:');
console.log('');
console.log('1️⃣  Mở Supabase Dashboard của bạn');
console.log('2️⃣  Vào SQL Editor');
console.log('3️⃣  Copy toàn bộ nội dung từ file: backend/scripts/populate-departments-vietnamese.sql');
console.log('4️⃣  Paste và chạy SQL script');
console.log('');

// Đọc file SQL
const sqlFilePath = path.join(__dirname, 'populate-departments-vietnamese.sql');

try {
  const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
  
  console.log('📄 File SQL đã sẵn sàng:');
  console.log(`   📍 Đường dẫn: ${sqlFilePath}`);
  console.log(`   📊 Kích thước: ${(sqlContent.length / 1024).toFixed(1)} KB`);
  console.log(`   📝 Số dòng: ${sqlContent.split('\n').length} dòng`);
  console.log('');
  
  console.log('🏥 Script này sẽ thêm 12 khoa/phòng ban:');
  console.log('   ✅ Khoa Tim mạch');
  console.log('   ✅ Khoa Thần kinh');
  console.log('   ✅ Khoa Nhi');
  console.log('   ✅ Khoa Sản phụ khoa');
  console.log('   ✅ Khoa Nội tổng hợp');
  console.log('   ✅ Khoa Ngoại tổng hợp');
  console.log('   ✅ Khoa Chấn thương chỉnh hình');
  console.log('   ✅ Khoa Cấp cứu');
  console.log('   ✅ Khoa Mắt');
  console.log('   ✅ Khoa Tai mũi họng');
  console.log('   ✅ Khoa Da liễu');
  console.log('   ✅ Khoa Hồi sức cấp cứu');
  console.log('');
  
  console.log('🔧 Thông tin mỗi khoa bao gồm:');
  console.log('   📞 Số điện thoại liên lạc');
  console.log('   📧 Email khoa');
  console.log('   📍 Vị trí/địa điểm');
  console.log('   📝 Mô tả chuyên khoa');
  console.log('   ✅ Trạng thái hoạt động');
  console.log('');
  
  console.log('💡 Sau khi chạy script:');
  console.log('   1. Kiểm tra database: node scripts/check-database-status.js');
  console.log('   2. Frontend sẽ sử dụng bảng departments thay vì departments_enum');
  console.log('   3. Refresh ứng dụng để thấy dữ liệu mới');
  console.log('');
  
  console.log('🚀 Sẵn sàng chạy script!');
  
} catch (err) {
  console.error('❌ Lỗi đọc file SQL:', err.message);
  console.log('');
  console.log('🔧 Kiểm tra:');
  console.log('   - File populate-departments-vietnamese.sql có tồn tại không?');
  console.log('   - Đường dẫn có đúng không?');
}

console.log('');
console.log('📞 Cần hỗ trợ? Hãy kiểm tra:');
console.log('   - Supabase connection');
console.log('   - Database permissions');
console.log('   - Bảng departments có tồn tại không');
console.log('');
console.log('🔄 Thay đổi quan trọng:');
console.log('   - Sử dụng bảng departments thay vì departments_enum');
console.log('   - Frontend đã được cập nhật để tương thích');
console.log('   - Không cần tạo bảng enum riêng');
