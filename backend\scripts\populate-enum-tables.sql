-- =====================================================
-- POPULATE ENUM TABLES WITH SAMPLE DATA
-- =====================================================
-- This script populates all 7 enum tables with comprehensive sample data
-- for the Hospital Management System

-- Clear existing data (optional - remove if you want to keep existing data)
-- TRUNCATE TABLE departments_enum RESTART IDENTITY CASCADE;
-- TRUNCATE TABLE specialties RESTART IDENTITY CASCADE;
-- TRUNCATE TABLE room_types RESTART IDENTITY CASCADE;
-- TRUNCATE TABLE diagnosis RESTART IDENTITY CASCADE;
-- TRUNCATE TABLE medications RESTART IDENTITY CASCADE;
-- TRUNCATE TABLE status_values RESTART IDENTITY CASCADE;
-- TRUNCATE TABLE payment_methods RESTART IDENTITY CASCADE;

-- =====================================================
-- 1. DEPARTMENTS_ENUM TABLE
-- =====================================================
INSERT INTO departments_enum (code, name_en, name_vi, description_en, description_vi, color_code, icon_name, sort_order, is_active) VALUES
('cardiology', 'Cardiology', 'Tim mạch', 'Heart and cardiovascular system care', 'Chăm sóc tim mạch và hệ thống tuần hoàn', '#e74c3c', 'heart', 1, true),
('neurology', 'Neurology', 'Thần kinh', 'Brain and nervous system disorders', 'Rối loạn não và hệ thần kinh', '#9b59b6', 'brain', 2, true),
('orthopedics', 'Orthopedics', 'Chấn thương chỉnh hình', 'Bone, joint, and muscle treatment', 'Điều trị xương, khớp và cơ', '#34495e', 'bone', 3, true),
('pediatrics', 'Pediatrics', 'Nhi khoa', 'Medical care for infants, children, and adolescents', 'Chăm sóc y tế cho trẻ sơ sinh, trẻ em và thanh thiếu niên', '#f39c12', 'baby', 4, true),
('internal_medicine', 'Internal Medicine', 'Nội tổng hợp', 'General internal medicine and adult care', 'Nội khoa tổng hợp và chăm sóc người lớn', '#27ae60', 'stethoscope', 5, true),
('surgery', 'Surgery', 'Phẫu thuật', 'Surgical procedures and operations', 'Các thủ thuật và ca phẫu thuật', '#c0392b', 'scalpel', 6, true),
('emergency', 'Emergency', 'Cấp cứu', 'Emergency and urgent care services', 'Dịch vụ cấp cứu và chăm sóc khẩn cấp', '#e67e22', 'ambulance', 7, true),
('radiology', 'Radiology', 'Chẩn đoán hình ảnh', 'Medical imaging and diagnostic services', 'Dịch vụ chẩn đoán hình ảnh y tế', '#3498db', 'x-ray', 8, true),
('laboratory', 'Laboratory', 'Xét nghiệm', 'Laboratory testing and analysis', 'Xét nghiệm và phân tích trong phòng thí nghiệm', '#16a085', 'flask', 9, true),
('pharmacy', 'Pharmacy', 'Dược', 'Pharmaceutical services and medication management', 'Dịch vụ dược phẩm và quản lý thuốc', '#8e44ad', 'pills', 10, true)
ON CONFLICT (code) DO UPDATE SET
    name_en = EXCLUDED.name_en,
    name_vi = EXCLUDED.name_vi,
    description_en = EXCLUDED.description_en,
    description_vi = EXCLUDED.description_vi,
    color_code = EXCLUDED.color_code,
    icon_name = EXCLUDED.icon_name,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- 2. SPECIALTIES TABLE (Update existing data)
-- =====================================================
INSERT INTO specialties (code, name_en, name_vi, description_en, description_vi, color_code, icon_name, sort_order, is_active) VALUES
('cardiology', 'Cardiology', 'Tim mạch', 'Heart and cardiovascular diseases', 'Bệnh tim và mạch máu', '#e74c3c', 'heart', 1, true),
('neurology', 'Neurology', 'Thần kinh', 'Brain and nervous system disorders', 'Rối loạn não và hệ thần kinh', '#9b59b6', 'brain', 2, true),
('orthopedics', 'Orthopedics', 'Chấn thương chỉnh hình', 'Bone, joint, and muscle disorders', 'Rối loạn xương, khớp và cơ', '#34495e', 'bone', 3, true),
('pediatrics', 'Pediatrics', 'Nhi khoa', 'Medical care for children', 'Chăm sóc y tế cho trẻ em', '#f39c12', 'baby', 4, true),
('internal_medicine', 'Internal Medicine', 'Nội tổng hợp', 'General internal medicine', 'Nội khoa tổng hợp', '#27ae60', 'stethoscope', 5, true),
('surgery', 'General Surgery', 'Phẫu thuật tổng hợp', 'General surgical procedures', 'Các thủ thuật phẫu thuật tổng hợp', '#c0392b', 'scalpel', 6, true),
('dermatology', 'Dermatology', 'Da liễu', 'Skin, hair, and nail disorders', 'Rối loạn da, tóc và móng', '#e67e22', 'skin', 7, true),
('ophthalmology', 'Ophthalmology', 'Mắt', 'Eye and vision care', 'Chăm sóc mắt và thị lực', '#3498db', 'eye', 8, true),
('ent', 'ENT (Otolaryngology)', 'Tai mũi họng', 'Ear, nose, and throat disorders', 'Rối loạn tai, mũi, họng', '#16a085', 'ear', 9, true),
('psychiatry', 'Psychiatry', 'Tâm thần', 'Mental health and psychiatric care', 'Chăm sóc sức khỏe tâm thần', '#8e44ad', 'brain-circuit', 10, true),
('obstetrics_gynecology', 'Obstetrics & Gynecology', 'Sản phụ khoa', 'Women\'s reproductive health', 'Sức khỏe sinh sản phụ nữ', '#e91e63', 'female', 11, true),
('urology', 'Urology', 'Tiết niệu', 'Urinary system and male reproductive health', 'Hệ tiết niệu và sức khỏe sinh sản nam', '#607d8b', 'kidney', 12, true)
ON CONFLICT (code) DO UPDATE SET
    name_en = EXCLUDED.name_en,
    name_vi = EXCLUDED.name_vi,
    description_en = EXCLUDED.description_en,
    description_vi = EXCLUDED.description_vi,
    color_code = EXCLUDED.color_code,
    icon_name = EXCLUDED.icon_name,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- 3. ROOM_TYPES TABLE (Update existing data)
-- =====================================================
INSERT INTO room_types (code, name_en, name_vi, description_en, description_vi, color_code, icon_name, sort_order, is_active) VALUES
('consultation', 'Consultation Room', 'Phòng khám', 'General consultation and examination room', 'Phòng khám và kiểm tra tổng quát', '#3498db', 'stethoscope', 1, true),
('surgery', 'Surgery Room', 'Phòng mổ', 'Operating room for surgical procedures', 'Phòng mổ cho các thủ thuật phẫu thuật', '#e74c3c', 'scalpel', 2, true),
('emergency', 'Emergency Room', 'Phòng cấp cứu', 'Emergency treatment room', 'Phòng điều trị cấp cứu', '#f39c12', 'ambulance', 3, true),
('ward', 'Ward Room', 'Phòng bệnh', 'Patient ward for recovery and monitoring', 'Phòng bệnh để hồi phục và theo dõi', '#27ae60', 'bed', 4, true),
('icu', 'ICU', 'Phòng hồi sức', 'Intensive Care Unit for critical patients', 'Phòng hồi sức tích cực cho bệnh nhân nguy kịch', '#9b59b6', 'heart-monitor', 5, true),
('laboratory', 'Laboratory', 'Phòng xét nghiệm', 'Laboratory for testing and analysis', 'Phòng thí nghiệm để xét nghiệm và phân tích', '#16a085', 'flask', 6, true),
('radiology', 'Radiology Room', 'Phòng chẩn đoán hình ảnh', 'Medical imaging and X-ray room', 'Phòng chẩn đoán hình ảnh và X-quang', '#34495e', 'x-ray', 7, true),
('pharmacy', 'Pharmacy', 'Phòng thuốc', 'Pharmacy for medication dispensing', 'Phòng thuốc để phát thuốc', '#8e44ad', 'pills', 8, true)
ON CONFLICT (code) DO UPDATE SET
    name_en = EXCLUDED.name_en,
    name_vi = EXCLUDED.name_vi,
    description_en = EXCLUDED.description_en,
    description_vi = EXCLUDED.description_vi,
    color_code = EXCLUDED.color_code,
    icon_name = EXCLUDED.icon_name,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- 4. DIAGNOSIS TABLE (Update existing data)
-- =====================================================
INSERT INTO diagnosis (code, name_en, name_vi, description_en, description_vi, color_code, icon_name, sort_order, is_active, icd_code) VALUES
('hypertension', 'Hypertension', 'Tăng huyết áp', 'High blood pressure', 'Huyết áp cao', '#e74c3c', 'heart-pulse', 1, true, 'I10'),
('diabetes_t2', 'Type 2 Diabetes', 'Đái tháo đường type 2', 'Type 2 diabetes mellitus', 'Đái tháo đường týp 2', '#f39c12', 'blood-drop', 2, true, 'E11'),
('common_cold', 'Common Cold', 'Cảm lạnh thông thường', 'Upper respiratory tract infection', 'Nhiễm trùng đường hô hấp trên', '#3498db', 'thermometer', 3, true, 'J00'),
('gastritis', 'Gastritis', 'Viêm dạ dày', 'Inflammation of stomach lining', 'Viêm niêm mạc dạ dày', '#27ae60', 'stomach', 4, true, 'K29'),
('migraine', 'Migraine', 'Đau nửa đầu', 'Severe recurring headache', 'Đau đầu tái phát nghiêm trọng', '#9b59b6', 'brain', 5, true, 'G43'),
('pneumonia', 'Pneumonia', 'Viêm phổi', 'Lung infection causing inflammation', 'Nhiễm trùng phổi gây viêm', '#e67e22', 'lungs', 6, true, 'J18'),
('arthritis', 'Arthritis', 'Viêm khớp', 'Joint inflammation and pain', 'Viêm và đau khớp', '#34495e', 'bone', 7, true, 'M13'),
('depression', 'Depression', 'Trầm cảm', 'Major depressive disorder', 'Rối loạn trầm cảm chính', '#8e44ad', 'brain-circuit', 8, true, 'F32'),
('asthma', 'Asthma', 'Hen suyễn', 'Chronic respiratory condition', 'Tình trạng hô hấp mãn tính', '#16a085', 'lungs', 9, true, 'J45'),
('fracture', 'Bone Fracture', 'Gãy xương', 'Broken or cracked bone', 'Xương bị gãy hoặc nứt', '#c0392b', 'bone-break', 10, true, 'S72')
ON CONFLICT (code) DO UPDATE SET
    name_en = EXCLUDED.name_en,
    name_vi = EXCLUDED.name_vi,
    description_en = EXCLUDED.description_en,
    description_vi = EXCLUDED.description_vi,
    color_code = EXCLUDED.color_code,
    icon_name = EXCLUDED.icon_name,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active,
    icd_code = EXCLUDED.icd_code,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- 5. MEDICATIONS TABLE (Update existing data)
-- =====================================================
INSERT INTO medications (code, name_en, name_vi, description_en, description_vi, color_code, icon_name, sort_order, is_active, drug_class, dosage_form) VALUES
('paracetamol', 'Paracetamol', 'Paracetamol', 'Pain reliever and fever reducer', 'Thuốc giảm đau và hạ sốt', '#3498db', 'pill', 1, true, 'Analgesic', 'Tablet'),
('amoxicillin', 'Amoxicillin', 'Amoxicillin', 'Antibiotic for bacterial infections', 'Kháng sinh điều trị nhiễm khuẩn', '#27ae60', 'capsule', 2, true, 'Antibiotic', 'Capsule'),
('ibuprofen', 'Ibuprofen', 'Ibuprofen', 'Anti-inflammatory pain reliever', 'Thuốc giảm đau chống viêm', '#e74c3c', 'pill', 3, true, 'NSAID', 'Tablet'),
('metformin', 'Metformin', 'Metformin', 'Diabetes medication', 'Thuốc điều trị đái tháo đường', '#f39c12', 'pill', 4, true, 'Antidiabetic', 'Tablet'),
('lisinopril', 'Lisinopril', 'Lisinopril', 'Blood pressure medication', 'Thuốc điều trị huyết áp', '#9b59b6', 'pill', 5, true, 'ACE Inhibitor', 'Tablet'),
('omeprazole', 'Omeprazole', 'Omeprazole', 'Stomach acid reducer', 'Thuốc giảm acid dạ dày', '#16a085', 'capsule', 6, true, 'PPI', 'Capsule'),
('salbutamol', 'Salbutamol', 'Salbutamol', 'Bronchodilator for asthma', 'Thuốc giãn phế quản cho hen suyễn', '#e67e22', 'inhaler', 7, true, 'Bronchodilator', 'Inhaler'),
('aspirin', 'Aspirin', 'Aspirin', 'Blood thinner and pain reliever', 'Thuốc làm loãng máu và giảm đau', '#34495e', 'pill', 8, true, 'Antiplatelet', 'Tablet'),
('cetirizine', 'Cetirizine', 'Cetirizine', 'Antihistamine for allergies', 'Thuốc kháng histamine điều trị dị ứng', '#8e44ad', 'pill', 9, true, 'Antihistamine', 'Tablet'),
('morphine', 'Morphine', 'Morphine', 'Strong pain medication', 'Thuốc giảm đau mạnh', '#c0392b', 'syringe', 10, true, 'Opioid', 'Injection')
ON CONFLICT (code) DO UPDATE SET
    name_en = EXCLUDED.name_en,
    name_vi = EXCLUDED.name_vi,
    description_en = EXCLUDED.description_en,
    description_vi = EXCLUDED.description_vi,
    color_code = EXCLUDED.color_code,
    icon_name = EXCLUDED.icon_name,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active,
    drug_class = EXCLUDED.drug_class,
    dosage_form = EXCLUDED.dosage_form,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- 6. STATUS_VALUES TABLE (Update existing data)
-- =====================================================
INSERT INTO status_values (code, name_en, name_vi, description_en, description_vi, color_code, icon_name, sort_order, is_active, applies_to) VALUES
('active', 'Active', 'Hoạt động', 'Currently active and available', 'Hiện đang hoạt động và có sẵn', '#27ae60', 'check-circle', 1, true, 'general'),
('inactive', 'Inactive', 'Không hoạt động', 'Currently inactive or unavailable', 'Hiện không hoạt động hoặc không có sẵn', '#95a5a6', 'x-circle', 2, true, 'general'),
('pending', 'Pending', 'Đang chờ', 'Waiting for approval or processing', 'Đang chờ phê duyệt hoặc xử lý', '#f39c12', 'clock', 3, true, 'general'),
('completed', 'Completed', 'Hoàn thành', 'Successfully completed', 'Đã hoàn thành thành công', '#27ae60', 'check', 4, true, 'appointment'),
('cancelled', 'Cancelled', 'Đã hủy', 'Cancelled or terminated', 'Đã hủy bỏ hoặc chấm dứt', '#e74c3c', 'x', 5, true, 'appointment'),
('scheduled', 'Scheduled', 'Đã lên lịch', 'Scheduled for future date', 'Đã lên lịch cho ngày tương lai', '#3498db', 'calendar', 6, true, 'appointment'),
('in_progress', 'In Progress', 'Đang tiến hành', 'Currently being processed', 'Hiện đang được xử lý', '#9b59b6', 'play', 7, true, 'appointment'),
('on_leave', 'On Leave', 'Đang nghỉ phép', 'Temporarily away or on leave', 'Tạm thời vắng mặt hoặc nghỉ phép', '#e67e22', 'pause', 8, true, 'doctor'),
('confirmed', 'Confirmed', 'Đã xác nhận', 'Confirmed and verified', 'Đã xác nhận và kiểm tra', '#16a085', 'shield-check', 9, true, 'appointment'),
('no_show', 'No Show', 'Không đến', 'Did not show up for appointment', 'Không đến cuộc hẹn', '#c0392b', 'user-x', 10, true, 'appointment')
ON CONFLICT (code) DO UPDATE SET
    name_en = EXCLUDED.name_en,
    name_vi = EXCLUDED.name_vi,
    description_en = EXCLUDED.description_en,
    description_vi = EXCLUDED.description_vi,
    color_code = EXCLUDED.color_code,
    icon_name = EXCLUDED.icon_name,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active,
    applies_to = EXCLUDED.applies_to,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- 7. PAYMENT_METHODS TABLE (Update existing data)
-- =====================================================
INSERT INTO payment_methods (code, name_en, name_vi, description_en, description_vi, color_code, icon_name, sort_order, is_active, requires_verification, processing_fee) VALUES
('cash', 'Cash', 'Tiền mặt', 'Cash payment', 'Thanh toán bằng tiền mặt', '#27ae60', 'banknote', 1, true, false, 0.00),
('credit_card', 'Credit Card', 'Thẻ tín dụng', 'Credit card payment', 'Thanh toán bằng thẻ tín dụng', '#3498db', 'credit-card', 2, true, true, 2.50),
('debit_card', 'Debit Card', 'Thẻ ghi nợ', 'Debit card payment', 'Thanh toán bằng thẻ ghi nợ', '#2980b9', 'credit-card', 3, true, true, 1.50),
('bank_transfer', 'Bank Transfer', 'Chuyển khoản ngân hàng', 'Electronic bank transfer', 'Chuyển khoản điện tử', '#8e44ad', 'building-bank', 4, true, true, 5.00),
('insurance', 'Insurance', 'Bảo hiểm', 'Insurance coverage payment', 'Thanh toán qua bảo hiểm', '#e74c3c', 'shield', 5, true, true, 0.00),
('mobile_payment', 'Mobile Payment', 'Thanh toán di động', 'Mobile wallet payment', 'Thanh toán qua ví điện tử', '#f39c12', 'smartphone', 6, true, true, 1.00),
('check', 'Check', 'Séc', 'Check payment', 'Thanh toán bằng séc', '#95a5a6', 'file-text', 7, true, true, 3.00),
('installment', 'Installment', 'Trả góp', 'Installment payment plan', 'Kế hoạch thanh toán trả góp', '#e67e22', 'calendar-days', 8, true, true, 10.00)
ON CONFLICT (code) DO UPDATE SET
    name_en = EXCLUDED.name_en,
    name_vi = EXCLUDED.name_vi,
    description_en = EXCLUDED.description_en,
    description_vi = EXCLUDED.description_vi,
    color_code = EXCLUDED.color_code,
    icon_name = EXCLUDED.icon_name,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active,
    requires_verification = EXCLUDED.requires_verification,
    processing_fee = EXCLUDED.processing_fee,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '✅ All enum tables have been populated with sample data!';
    RAISE NOTICE '📊 Tables updated: departments_enum, specialties, room_types, diagnosis, medications, status_values, payment_methods';
    RAISE NOTICE '🔄 You can now test your frontend enum data fetching.';
END $$;
