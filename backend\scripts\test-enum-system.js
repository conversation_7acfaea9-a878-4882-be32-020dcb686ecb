#!/usr/bin/env node

/**
 * Script để test toàn bộ hệ thống enum Vietnamese-only
 */

const { createClient } = require('@supabase/supabase-js');
const path = require('path');

require('dotenv').config({ path: path.join(__dirname, '../../.env.local') });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testEnumSystem() {
  console.log('🧪 TESTING ENUM SYSTEM - VIETNAMESE ONLY');
  console.log('=====================================================');
  console.log('');

  const enumTables = [
    { name: 'departments', displayName: 'Khoa/Phòng ban', keyField: 'department_id', nameField: 'name' },
    { name: 'specialties', displayName: 'Chuyên khoa', keyField: 'code', nameField: 'name_vi' },
    { name: 'room_types', displayName: 'Loại phòng', keyField: 'code', nameField: 'name_vi' },
    { name: 'diagnosis', displayName: 'Chẩn đoán', keyField: 'code', nameField: 'name_vi' },
    { name: 'medications', displayName: 'Thuốc', keyField: 'code', nameField: 'name_vi' },
    { name: 'status_values', displayName: 'Trạng thái', keyField: 'code', nameField: 'name_vi' },
    { name: 'payment_methods', displayName: 'Phương thức thanh toán', keyField: 'code', nameField: 'name_vi' }
  ];

  let allTestsPassed = true;

  for (const table of enumTables) {
    console.log(`📋 Testing ${table.displayName} (${table.name})...`);
    
    try {
      const { data, error } = await supabase
        .from(table.name)
        .select('*')
        .eq('is_active', true)
        .limit(5);
      
      if (error) {
        console.error(`   ❌ Error: ${error.message}`);
        allTestsPassed = false;
        continue;
      }
      
      if (!data || data.length === 0) {
        console.log(`   ⚠️  No data found - need to populate`);
        allTestsPassed = false;
        continue;
      }
      
      console.log(`   ✅ Found ${data.length} records`);
      
      // Show sample data
      const sample = data[0];
      const key = sample[table.keyField];
      const name = sample[table.nameField];
      console.log(`   📝 Sample: ${key} - ${name}`);
      
      // Check required fields
      const requiredFields = ['is_active'];
      if (table.name !== 'departments') {
        requiredFields.push('sort_order');
      }
      
      const missingFields = requiredFields.filter(field => !(field in sample));
      if (missingFields.length > 0) {
        console.log(`   ⚠️  Missing fields: ${missingFields.join(', ')}`);
      }
      
    } catch (err) {
      console.error(`   ❌ Error: ${err.message}`);
      allTestsPassed = false;
    }
    
    console.log('');
  }

  // Test enum conversion for departments
  console.log('🔄 Testing departments enum conversion...');
  try {
    const { data: departments, error } = await supabase
      .from('departments')
      .select('department_id, name, description')
      .eq('is_active', true)
      .limit(3);
    
    if (error) throw error;
    
    if (departments && departments.length > 0) {
      console.log('   ✅ Departments conversion test:');
      departments.forEach(dept => {
        const enumFormat = {
          value: dept.department_id,
          label: dept.name,
          description: dept.description
        };
        console.log(`      ${enumFormat.value}: ${enumFormat.label}`);
      });
    } else {
      console.log('   ⚠️  No departments found for conversion test');
      allTestsPassed = false;
    }
  } catch (err) {
    console.error(`   ❌ Departments conversion error: ${err.message}`);
    allTestsPassed = false;
  }

  console.log('');
  console.log('📊 SUMMARY');
  console.log('=====================================================');
  
  if (allTestsPassed) {
    console.log('✅ All tests passed! Enum system is ready.');
    console.log('');
    console.log('🚀 Next steps:');
    console.log('   1. Start your frontend: npm run dev');
    console.log('   2. Test doctor registration form');
    console.log('   3. Check enum dropdowns are populated');
  } else {
    console.log('❌ Some tests failed. Please check the issues above.');
    console.log('');
    console.log('🔧 To fix:');
    console.log('   1. Run populate-departments-vietnamese.sql');
    console.log('   2. Run populate-enum-tables-vietnamese.sql');
    console.log('   3. Re-run this test');
  }
  
  console.log('');
  console.log('📞 Need help? Check:');
  console.log('   - Supabase connection');
  console.log('   - Database permissions');
  console.log('   - SQL scripts execution');
}

testEnumSystem().catch(console.error);
