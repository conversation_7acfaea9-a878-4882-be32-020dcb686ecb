#!/usr/bin/env node

/**
 * Verify Bilingual System Removal
 * 
 * This script verifies that all bilingual features have been completely removed
 * from the hospital management system.
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables:');
  console.error('   NEXT_PUBLIC_SUPABASE_URL');
  console.error('   SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkDatabaseStructure() {
  console.log('🔍 Checking database structure...');
  
  const issues = [];
  
  try {
    // Check if languages_spoken column exists in doctors table
    const { data: doctor<PERSON>olumns, error: doctor<PERSON>rror } = await supabase
      .rpc('exec_sql', { 
        sql_query: `
          SELECT column_name 
          FROM information_schema.columns 
          WHERE table_name = 'doctors' AND column_name = 'languages_spoken'
        `
      });
    
    if (doctorError) {
      console.log('   ⚠️  Could not check doctors table structure');
    } else if (doctorColumns && doctorColumns.length > 0) {
      issues.push('❌ languages_spoken column still exists in doctors table');
    } else {
      console.log('   ✅ languages_spoken column removed from doctors table');
    }
    
    // Check enum tables for English columns
    const enumTables = [
      'departments_enum', 'specialties', 'room_types', 
      'diagnosis', 'medications', 'status_values', 'payment_methods'
    ];
    
    for (const tableName of enumTables) {
      const { data: columns, error } = await supabase
        .rpc('exec_sql', { 
          sql_query: `
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = '${tableName}' 
            AND column_name IN ('name_en', 'description_en')
          `
        });
      
      if (error) {
        console.log(`   ⚠️  Could not check ${tableName} structure`);
      } else if (columns && columns.length > 0) {
        issues.push(`❌ ${tableName} still has English columns`);
      } else {
        console.log(`   ✅ ${tableName} converted to Vietnamese-only`);
      }
    }
    
  } catch (error) {
    console.error(`❌ Error checking database: ${error.message}`);
  }
  
  return issues;
}

function checkCodeFiles() {
  console.log('\n🔍 Checking code files...');
  
  const issues = [];
  
  // Check if translation service still exists
  const translationServicePath = path.join(__dirname, '../shared/src/services/translation.service.ts');
  if (fs.existsSync(translationServicePath)) {
    issues.push('❌ translation.service.ts still exists');
  } else {
    console.log('   ✅ translation.service.ts removed');
  }
  
  // Check if bilingual demo still exists
  const bilingualDemoPath = path.join(__dirname, '../examples/bilingual-demo.ts');
  if (fs.existsSync(bilingualDemoPath)) {
    issues.push('❌ bilingual-demo.ts still exists');
  } else {
    console.log('   ✅ bilingual-demo.ts removed');
  }
  
  // Check validators for languages_spoken
  const validatorsPath = path.join(__dirname, '../shared/src/validators/all-tables.validators.ts');
  if (fs.existsSync(validatorsPath)) {
    const content = fs.readFileSync(validatorsPath, 'utf8');
    if (content.includes('languages_spoken')) {
      issues.push('❌ languages_spoken still in validators');
    } else {
      console.log('   ✅ languages_spoken removed from validators');
    }
  }
  
  // Check doctor validators
  const doctorValidatorsPath = path.join(__dirname, '../services/doctor-service/src/validators/doctor.validators.ts');
  if (fs.existsSync(doctorValidatorsPath)) {
    const content = fs.readFileSync(doctorValidatorsPath, 'utf8');
    if (content.includes('languages_spoken')) {
      issues.push('❌ languages_spoken still in doctor validators');
    } else {
      console.log('   ✅ languages_spoken removed from doctor validators');
    }
  }
  
  return issues;
}

async function checkSampleData() {
  console.log('\n🔍 Checking sample data...');
  
  try {
    // Check departments_enum data
    const { data: depts, error: deptError } = await supabase
      .from('departments_enum')
      .select('code, name, description')
      .limit(3);
    
    if (!deptError && depts && depts.length > 0) {
      console.log('   ✅ departments_enum has Vietnamese data:');
      depts.forEach(dept => {
        console.log(`      ${dept.code}: ${dept.name}`);
      });
    }
    
    // Check specialties data
    const { data: specs, error: specError } = await supabase
      .from('specialties')
      .select('code, name, description')
      .limit(3);
    
    if (!specError && specs && specs.length > 0) {
      console.log('   ✅ specialties has Vietnamese data:');
      specs.forEach(spec => {
        console.log(`      ${spec.code}: ${spec.name}`);
      });
    }
    
  } catch (error) {
    console.error(`❌ Error checking sample data: ${error.message}`);
  }
}

async function main() {
  console.log('🚀 Verifying bilingual system removal...');
  console.log('==========================================');
  
  const allIssues = [];
  
  // Check database structure
  const dbIssues = await checkDatabaseStructure();
  allIssues.push(...dbIssues);
  
  // Check code files
  const codeIssues = checkCodeFiles();
  allIssues.push(...codeIssues);
  
  // Check sample data
  await checkSampleData();
  
  // Summary
  console.log('\n📊 VERIFICATION SUMMARY');
  console.log('========================');
  
  if (allIssues.length === 0) {
    console.log('🎉 SUCCESS! Bilingual system completely removed!');
    console.log('✅ All checks passed');
    console.log('✅ Database converted to Vietnamese-only');
    console.log('✅ Code files cleaned up');
    console.log('✅ System ready for Vietnamese-only operation');
  } else {
    console.log('⚠️  ISSUES FOUND:');
    allIssues.forEach(issue => console.log(`   ${issue}`));
    console.log('\n💡 Please address these issues before proceeding.');
  }
  
  console.log('\n📝 Next steps:');
  console.log('   1. Test frontend components');
  console.log('   2. Test API endpoints');
  console.log('   3. Verify user registration/login flows');
  console.log('   4. Check enum data display in UI');
}

main().catch(error => {
  console.error('❌ Verification failed:', error);
  process.exit(1);
});
