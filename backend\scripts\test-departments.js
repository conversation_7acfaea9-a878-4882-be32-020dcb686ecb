#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
const path = require('path');

require('dotenv').config({ path: path.join(__dirname, '../../.env.local') });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('🔍 Testing departments table...');

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables');
  console.log('Please check .env.local file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testDepartments() {
  try {
    console.log('📡 Connecting to Supabase...');
    
    // Test connection
    const { data, error } = await supabase
      .from('departments')
      .select('department_id, name, description')
      .limit(5);
    
    if (error) {
      console.error('❌ Error:', error.message);
      return;
    }
    
    console.log('✅ Connection successful!');
    console.log(`📊 Found ${data?.length || 0} departments`);
    
    if (data && data.length > 0) {
      console.log('\n📋 Current departments:');
      data.forEach((dept, index) => {
        console.log(`   ${index + 1}. ${dept.department_id}: ${dept.name}`);
      });
    } else {
      console.log('\n⚠️  No departments found. Need to populate data.');
    }
    
    console.log('\n💡 Next steps:');
    console.log('1. Copy SQL from: backend/scripts/populate-departments-vietnamese.sql');
    console.log('2. Paste into Supabase SQL Editor');
    console.log('3. Run the script');
    
  } catch (err) {
    console.error('❌ Error:', err.message);
  }
}

testDepartments();
