-- ============================================================================
-- POPULATE ENUM TABLES - VIETNAM ONLY VERSION
-- ============================================================================
-- This script populates all enum tables with Vietnamese-only data
-- Run this AFTER running remove-bilingual-system.sql

-- =====================================================
-- 1. DEPARTMENTS_ENUM TABLE
-- =====================================================
INSERT INTO departments_enum (code, name, description, color_code, icon_name, sort_order, is_active) VALUES
('cardiology', 'Tim mạch', 'Chăm sóc tim mạch và hệ thống tuần hoàn', '#e74c3c', 'heart', 1, true),
('neurology', 'Thần kinh', 'Rối loạn não và hệ thần kinh', '#9b59b6', 'brain', 2, true),
('orthopedics', 'Chấn thương chỉnh hình', 'Điều trị xương, khớp và cơ', '#34495e', 'bone', 3, true),
('pediatrics', 'Nhi khoa', 'Chăm sóc sức khỏe trẻ em', '#f39c12', 'baby', 4, true),
('dermatology', 'Da liễu', 'Điều trị các bệnh về da', '#e67e22', 'skin', 5, true),
('ophthalmology', 'Mắt', 'Chăm sóc mắt và thị lực', '#3498db', 'eye', 6, true),
('emergency', 'Cấp cứu', 'Dịch vụ y tế khẩn cấp', '#c0392b', 'ambulance', 7, true),
('radiology', 'Chẩn đoán hình ảnh', 'Dịch vụ chẩn đoán hình ảnh y tế', '#3498db', 'x-ray', 8, true),
('laboratory', 'Xét nghiệm', 'Xét nghiệm và phân tích trong phòng thí nghiệm', '#16a085', 'flask', 9, true),
('pharmacy', 'Dược', 'Dịch vụ dược phẩm và quản lý thuốc', '#8e44ad', 'pills', 10, true)
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    color_code = EXCLUDED.color_code,
    icon_name = EXCLUDED.icon_name,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- 2. SPECIALTIES TABLE
-- =====================================================
INSERT INTO specialties (code, name, description, color_code, icon_name, sort_order, is_active) VALUES
('internal_medicine', 'Nội khoa tổng hợp', 'Chẩn đoán và điều trị bệnh nội khoa', '#2ecc71', 'stethoscope', 1, true),
('surgery', 'Phẫu thuật tổng hợp', 'Phẫu thuật và can thiệp y tế', '#e74c3c', 'scalpel', 2, true),
('cardiology', 'Tim mạch', 'Chuyên khoa tim mạch và hệ tuần hoàn', '#e74c3c', 'heart', 3, true),
('neurology', 'Thần kinh', 'Chuyên khoa thần kinh và não bộ', '#9b59b6', 'brain', 4, true),
('orthopedics', 'Chấn thương chỉnh hình', 'Phẫu thuật xương khớp và chấn thương', '#34495e', 'bone', 5, true),
('pediatrics', 'Nhi khoa', 'Chăm sóc y tế cho trẻ em', '#f39c12', 'baby', 6, true),
('gynecology', 'Phụ khoa', 'Chăm sóc sức khỏe phụ nữ', '#e91e63', 'female', 7, true),
('dermatology', 'Da liễu', 'Điều trị bệnh da và thẩm mỹ', '#e67e22', 'skin', 8, true),
('ent', 'Tai mũi họng', 'Rối loạn tai, mũi, họng', '#16a085', 'ear', 9, true),
('psychiatry', 'Tâm thần', 'Chăm sóc sức khỏe tâm thần', '#8e44ad', 'brain-circuit', 10, true),
('obstetrics_gynecology', 'Sản phụ khoa', 'Sức khỏe sinh sản phụ nữ', '#e91e63', 'female', 11, true),
('urology', 'Tiết niệu', 'Hệ tiết niệu và sức khỏe sinh sản nam', '#607d8b', 'kidney', 12, true)
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    color_code = EXCLUDED.color_code,
    icon_name = EXCLUDED.icon_name,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- 3. ROOM_TYPES TABLE
-- =====================================================
INSERT INTO room_types (code, name, description, color_code, icon_name, sort_order, is_active) VALUES
('phong_kham', 'Phòng khám', 'Phòng khám bệnh tổng quát', '#2ecc71', 'stethoscope', 1, true),
('phong_benh', 'Phòng bệnh', 'Phòng nằm viện cho bệnh nhân', '#3498db', 'bed', 2, true),
('phong_phau_thuat', 'Phòng phẫu thuật', 'Phòng mổ và phẫu thuật', '#e74c3c', 'scalpel', 3, true),
('phong_cap_cuu', 'Phòng cấp cứu', 'Phòng cấp cứu khẩn cấp', '#c0392b', 'ambulance', 4, true),
('phong_hoi_suc', 'Phòng hồi sức', 'Phòng hồi sức tích cực cho bệnh nhân nguy kịch', '#9b59b6', 'heart-monitor', 5, true),
('phong_xet_nghiem', 'Phòng xét nghiệm', 'Phòng thí nghiệm để xét nghiệm và phân tích', '#16a085', 'flask', 6, true),
('phong_chan_doan_hinh_anh', 'Phòng chẩn đoán hình ảnh', 'Phòng chẩn đoán hình ảnh và X-quang', '#34495e', 'x-ray', 7, true),
('phong_thuoc', 'Phòng thuốc', 'Phòng thuốc để phát thuốc', '#8e44ad', 'pills', 8, true)
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    color_code = EXCLUDED.color_code,
    icon_name = EXCLUDED.icon_name,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- 4. DIAGNOSIS TABLE
-- =====================================================
INSERT INTO diagnosis (code, name, description, color_code, icon_name, sort_order, is_active, icd_code) VALUES
('hypertension', 'Tăng huyết áp', 'Huyết áp cao', '#e74c3c', 'heart-pulse', 1, true, 'I10'),
('diabetes_t2', 'Đái tháo đường type 2', 'Đái tháo đường týp 2', '#f39c12', 'blood-drop', 2, true, 'E11'),
('common_cold', 'Cảm lạnh thông thường', 'Nhiễm trùng đường hô hấp trên', '#3498db', 'thermometer', 3, true, 'J00'),
('gastritis', 'Viêm dạ dày', 'Viêm niêm mạc dạ dày', '#e67e22', 'stomach', 4, true, 'K29'),
('migraine', 'Đau nửa đầu', 'Cơn đau đầu migraine', '#9b59b6', 'brain', 5, true, 'G43'),
('asthma', 'Hen suyễn', 'Bệnh hen suyễn phế quản', '#27ae60', 'lungs', 6, true, 'J45'),
('depression', 'Trầm cảm', 'Rối loạn trầm cảm', '#8e44ad', 'brain-circuit', 7, true, 'F32'),
('arthritis', 'Viêm khớp', 'Viêm khớp và đau khớp', '#34495e', 'bone', 8, true, 'M13')
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    color_code = EXCLUDED.color_code,
    icon_name = EXCLUDED.icon_name,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active,
    icd_code = EXCLUDED.icd_code,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- 5. MEDICATIONS TABLE
-- =====================================================
INSERT INTO medications (code, name, description, color_code, icon_name, sort_order, is_active, dosage_form, strength) VALUES
('paracetamol', 'Paracetamol', 'Thuốc giảm đau và hạ sốt', '#3498db', 'pill', 1, true, 'viên nén', '500mg'),
('ibuprofen', 'Ibuprofen', 'Thuốc chống viêm và giảm đau', '#e74c3c', 'pill', 2, true, 'viên nang', '400mg'),
('amoxicillin', 'Amoxicillin', 'Kháng sinh điều trị nhiễm khuẩn', '#27ae60', 'pill', 3, true, 'viên nang', '500mg'),
('metformin', 'Metformin', 'Thuốc điều trị đái tháo đường', '#f39c12', 'pill', 4, true, 'viên nén', '850mg'),
('lisinopril', 'Lisinopril', 'Thuốc điều trị tăng huyết áp', '#e74c3c', 'pill', 5, true, 'viên nén', '10mg'),
('omeprazole', 'Omeprazole', 'Thuốc điều trị loét dạ dày', '#e67e22', 'pill', 6, true, 'viên nang', '20mg'),
('salbutamol', 'Salbutamol', 'Thuốc xịt điều trị hen suyễn', '#27ae60', 'inhaler', 7, true, 'xịt', '100mcg'),
('aspirin', 'Aspirin', 'Thuốc chống đông máu và giảm đau', '#95a5a6', 'pill', 8, true, 'viên nén', '100mg')
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    color_code = EXCLUDED.color_code,
    icon_name = EXCLUDED.icon_name,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active,
    dosage_form = EXCLUDED.dosage_form,
    strength = EXCLUDED.strength,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- 6. STATUS_VALUES TABLE
-- =====================================================
INSERT INTO status_values (code, name, description, color_code, icon_name, sort_order, is_active, applies_to) VALUES
('active', 'Hoạt động', 'Hiện đang hoạt động và có sẵn', '#27ae60', 'check-circle', 1, true, 'general'),
('inactive', 'Không hoạt động', 'Hiện không hoạt động hoặc không có sẵn', '#95a5a6', 'x-circle', 2, true, 'general'),
('pending', 'Đang chờ', 'Đang chờ xử lý hoặc phê duyệt', '#f39c12', 'clock', 3, true, 'general'),
('completed', 'Hoàn thành', 'Đã hoàn thành thành công', '#27ae60', 'check', 4, true, 'general'),
('cancelled', 'Đã hủy', 'Đã bị hủy bỏ', '#e74c3c', 'x', 5, true, 'general'),
('scheduled', 'Đã lên lịch', 'Đã được lên lịch', '#3498db', 'calendar', 6, true, 'appointment'),
('in_progress', 'Đang tiến hành', 'Hiện đang được thực hiện', '#e67e22', 'play', 7, true, 'general'),
('on_leave', 'Đang nghỉ phép', 'Tạm thời vắng mặt hoặc nghỉ phép', '#e67e22', 'pause', 8, true, 'doctor'),
('confirmed', 'Đã xác nhận', 'Đã xác nhận và kiểm tra', '#16a085', 'shield-check', 9, true, 'appointment'),
('no_show', 'Không đến', 'Không đến cuộc hẹn', '#c0392b', 'user-x', 10, true, 'appointment')
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    color_code = EXCLUDED.color_code,
    icon_name = EXCLUDED.icon_name,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active,
    applies_to = EXCLUDED.applies_to,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- 7. PAYMENT_METHODS TABLE
-- =====================================================
INSERT INTO payment_methods (code, name, description, color_code, icon_name, sort_order, is_active, requires_verification, processing_fee) VALUES
('tien_mat', 'Tiền mặt', 'Thanh toán bằng tiền mặt', '#27ae60', 'banknote', 1, true, false, 0.00),
('the_tin_dung', 'Thẻ tín dụng', 'Thanh toán qua thẻ tín dụng', '#3498db', 'credit-card', 2, true, true, 2.50),
('the_ghi_no', 'Thẻ ghi nợ', 'Thanh toán qua thẻ ghi nợ', '#2ecc71', 'credit-card', 3, true, true, 1.50),
('chuyen_khoan', 'Chuyển khoản', 'Chuyển khoản ngân hàng', '#34495e', 'bank', 4, true, true, 5.00),
('bao_hiem', 'Bảo hiểm', 'Thanh toán qua bảo hiểm', '#e74c3c', 'shield', 5, true, true, 0.00),
('vi_dien_tu', 'Ví điện tử', 'Thanh toán qua ví điện tử', '#f39c12', 'smartphone', 6, true, true, 1.00),
('sec', 'Séc', 'Thanh toán bằng séc', '#95a5a6', 'file-text', 7, true, true, 3.00),
('tra_gop', 'Trả góp', 'Kế hoạch thanh toán trả góp', '#e67e22', 'calendar-days', 8, true, true, 10.00)
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    color_code = EXCLUDED.color_code,
    icon_name = EXCLUDED.icon_name,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active,
    requires_verification = EXCLUDED.requires_verification,
    processing_fee = EXCLUDED.processing_fee,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- VERIFICATION
-- =====================================================
SELECT 'Enum tables populated successfully with Vietnamese-only data!' as message;

-- Show sample data from each table
SELECT 'departments_enum sample:' as table_name, code, name FROM departments_enum LIMIT 3;
SELECT 'specialties sample:' as table_name, code, name FROM specialties LIMIT 3;
SELECT 'room_types sample:' as table_name, code, name FROM room_types LIMIT 3;
SELECT 'diagnosis sample:' as table_name, code, name FROM diagnosis LIMIT 3;
SELECT 'medications sample:' as table_name, code, name FROM medications LIMIT 3;
SELECT 'status_values sample:' as table_name, code, name FROM status_values LIMIT 3;
SELECT 'payment_methods sample:' as table_name, code, name FROM payment_methods LIMIT 3;
