#!/usr/bin/env node

/**
 * <PERSON>ript để kiểm tra cấu trúc bảng enum
 */

const { createClient } = require('@supabase/supabase-js');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../../.env.local') });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkTableStructure() {
  console.log('🔍 Checking enum table structures...\n');

  const enumTables = [
    'departments_enum',
    'specialties',
    'room_types',
    'diagnosis',
    'medications',
    'status_values',
    'payment_methods'
  ];

  for (const tableName of enumTables) {
    console.log(`📋 Table: ${tableName}`);

    try {
      // Try to select from table to see what columns exist
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);

      if (error) {
        console.error(`   ❌ Error: ${error.message}`);

        // If error mentions column, it might give us clues
        if (error.message.includes('column') && error.message.includes('does not exist')) {
          console.log('   💡 This suggests the table exists but columns are different');
        }
        continue;
      }

      console.log('   ✅ Table accessible');
      console.log(`   📊 Sample data count: ${data?.length || 0}`);

      // If we have data, show the structure
      if (data && data.length > 0) {
        console.log('   🔍 Available columns:');
        Object.keys(data[0]).forEach(key => {
          console.log(`      - ${key}: ${typeof data[0][key]}`);
        });
      } else {
        console.log('   📝 Table is empty, trying to insert test data to see column structure...');

        // Try a simple insert to see what columns are expected
        const testData = {
          code: 'test',
          name_vi: 'Test',
          sort_order: 1,
          is_active: true
        };

        const { error: insertError } = await supabase
          .from(tableName)
          .insert(testData);

        if (insertError) {
          console.log(`   ⚠️  Insert error (shows expected structure): ${insertError.message}`);
        } else {
          console.log('   ✅ Test insert successful - deleting test data...');
          await supabase.from(tableName).delete().eq('code', 'test');
        }
      }

    } catch (err) {
      console.error(`   ❌ Error checking ${tableName}:`, err.message);
    }

    console.log('');
  }
}

checkTableStructure().catch(console.error);
