-- =====================================================
-- POPULATE ENUM TABLES - VIETNAMESE ONLY VERSION
-- =====================================================
-- Phiên bản đơn giản chỉ sử dụng tiếng Việt
-- Dành cho hệ thống bệnh viện tại Việt Nam

-- =====================================================
-- 1. DEPARTMENTS - Sử dụng bảng departments thay vì departments_enum
-- =====================================================
-- Lưu ý: Departments được populate từ file riêng: populate-departments-vietnamese.sql
-- Bảng departments có cấu trúc khác với enum tables nên cần script riêng

-- =====================================================
-- 2. SPECIALTIES TABLE - Chuyên khoa
-- =====================================================
INSERT INTO specialties (code, name_vi, description_vi, color_code, icon_name, sort_order, is_active) VALUES
('tim_mach', 'Tim mạch', 'Bệnh tim và mạch máu', '#e74c3c', 'heart', 1, true),
('than_kinh', 'Thần kinh', 'Rối loạn não và hệ thần kinh', '#9b59b6', 'brain', 2, true),
('chan_thuong_chinh_hinh', 'Chấn thương chỉnh hình', 'Rối loạn xương, khớp và cơ', '#34495e', 'bone', 3, true),
('nhi_khoa', 'Nhi khoa', 'Chăm sóc y tế cho trẻ em', '#f39c12', 'baby', 4, true),
('noi_tong_hop', 'Nội tổng hợp', 'Nội khoa tổng hợp', '#27ae60', 'stethoscope', 5, true),
('phau_thuat_tong_hop', 'Phẫu thuật tổng hợp', 'Các thủ thuật phẫu thuật tổng hợp', '#c0392b', 'scalpel', 6, true),
('da_lieu', 'Da liễu', 'Rối loạn da, tóc và móng', '#e67e22', 'skin', 7, true),
('mat', 'Mắt', 'Chăm sóc mắt và thị lực', '#3498db', 'eye', 8, true),
('tai_mui_hong', 'Tai mũi họng', 'Rối loạn tai, mũi, họng', '#16a085', 'ear', 9, true),
('tam_than', 'Tâm thần', 'Chăm sóc sức khỏe tâm thần', '#8e44ad', 'brain-circuit', 10, true),
('san_phu_khoa', 'Sản phụ khoa', 'Sức khỏe sinh sản phụ nữ', '#e91e63', 'female', 11, true),
('tiet_nieu', 'Tiết niệu', 'Hệ tiết niệu và sức khỏe sinh sản nam', '#607d8b', 'kidney', 12, true)
ON CONFLICT (code) DO UPDATE SET
    name_vi = EXCLUDED.name_vi,
    description_vi = EXCLUDED.description_vi,
    color_code = EXCLUDED.color_code,
    icon_name = EXCLUDED.icon_name,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- 3. ROOM_TYPES TABLE - Loại phòng
-- =====================================================
INSERT INTO room_types (code, name_vi, description_vi, color_code, icon_name, sort_order, is_active) VALUES
('phong_kham', 'Phòng khám', 'Phòng khám và kiểm tra tổng quát', '#3498db', 'stethoscope', 1, true),
('phong_mo', 'Phòng mổ', 'Phòng mổ cho các thủ thuật phẫu thuật', '#e74c3c', 'scalpel', 2, true),
('phong_cap_cuu', 'Phòng cấp cứu', 'Phòng điều trị cấp cứu', '#f39c12', 'ambulance', 3, true),
('phong_benh', 'Phòng bệnh', 'Phòng bệnh để hồi phục và theo dõi', '#27ae60', 'bed', 4, true),
('phong_hoi_suc', 'Phòng hồi sức', 'Phòng hồi sức tích cực cho bệnh nhân nguy kịch', '#9b59b6', 'heart-monitor', 5, true),
('phong_xet_nghiem', 'Phòng xét nghiệm', 'Phòng thí nghiệm để xét nghiệm và phân tích', '#16a085', 'flask', 6, true),
('phong_chan_doan_hinh_anh', 'Phòng chẩn đoán hình ảnh', 'Phòng chẩn đoán hình ảnh và X-quang', '#34495e', 'x-ray', 7, true),
('phong_thuoc', 'Phòng thuốc', 'Phòng thuốc để phát thuốc', '#8e44ad', 'pills', 8, true)
ON CONFLICT (code) DO UPDATE SET
    name_vi = EXCLUDED.name_vi,
    description_vi = EXCLUDED.description_vi,
    color_code = EXCLUDED.color_code,
    icon_name = EXCLUDED.icon_name,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- 4. DIAGNOSIS TABLE - Chẩn đoán
-- =====================================================
INSERT INTO diagnosis (code, name_vi, description_vi, color_code, icon_name, sort_order, is_active, icd_code) VALUES
('tang_huyet_ap', 'Tăng huyết áp', 'Huyết áp cao', '#e74c3c', 'heart-pulse', 1, true, 'I10'),
('dai_thao_duong_type2', 'Đái tháo đường type 2', 'Đái tháo đường týp 2', '#f39c12', 'blood-drop', 2, true, 'E11'),
('cam_lanh_thuong', 'Cảm lạnh thông thường', 'Nhiễm trùng đường hô hấp trên', '#3498db', 'thermometer', 3, true, 'J00'),
('viem_da_day', 'Viêm dạ dày', 'Viêm niêm mạc dạ dày', '#27ae60', 'stomach', 4, true, 'K29'),
('dau_nua_dau', 'Đau nửa đầu', 'Đau đầu tái phát nghiêm trọng', '#9b59b6', 'brain', 5, true, 'G43'),
('viem_phoi', 'Viêm phổi', 'Nhiễm trùng phổi gây viêm', '#e67e22', 'lungs', 6, true, 'J18'),
('viem_khop', 'Viêm khớp', 'Viêm và đau khớp', '#34495e', 'bone', 7, true, 'M13'),
('tram_cam', 'Trầm cảm', 'Rối loạn trầm cảm chính', '#8e44ad', 'brain-circuit', 8, true, 'F32'),
('hen_suyen', 'Hen suyễn', 'Tình trạng hô hấp mãn tính', '#16a085', 'lungs', 9, true, 'J45'),
('gay_xuong', 'Gãy xương', 'Xương bị gãy hoặc nứt', '#c0392b', 'bone-break', 10, true, 'S72')
ON CONFLICT (code) DO UPDATE SET
    name_vi = EXCLUDED.name_vi,
    description_vi = EXCLUDED.description_vi,
    color_code = EXCLUDED.color_code,
    icon_name = EXCLUDED.icon_name,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active,
    icd_code = EXCLUDED.icd_code,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- 5. MEDICATIONS TABLE - Thuốc
-- =====================================================
INSERT INTO medications (code, name_vi, description_vi, color_code, icon_name, sort_order, is_active, drug_class, dosage_form) VALUES
('paracetamol', 'Paracetamol', 'Thuốc giảm đau và hạ sốt', '#3498db', 'pill', 1, true, 'Thuốc giảm đau', 'Viên nén'),
('amoxicillin', 'Amoxicillin', 'Kháng sinh điều trị nhiễm khuẩn', '#27ae60', 'capsule', 2, true, 'Kháng sinh', 'Viên nang'),
('ibuprofen', 'Ibuprofen', 'Thuốc giảm đau chống viêm', '#e74c3c', 'pill', 3, true, 'Thuốc chống viêm', 'Viên nén'),
('metformin', 'Metformin', 'Thuốc điều trị đái tháo đường', '#f39c12', 'pill', 4, true, 'Thuốc chống đái tháo đường', 'Viên nén'),
('lisinopril', 'Lisinopril', 'Thuốc điều trị huyết áp', '#9b59b6', 'pill', 5, true, 'Thuốc hạ huyết áp', 'Viên nén'),
('omeprazole', 'Omeprazole', 'Thuốc giảm acid dạ dày', '#16a085', 'capsule', 6, true, 'Thuốc dạ dày', 'Viên nang'),
('salbutamol', 'Salbutamol', 'Thuốc giãn phế quản cho hen suyễn', '#e67e22', 'inhaler', 7, true, 'Thuốc giãn phế quản', 'Xịt hít'),
('aspirin', 'Aspirin', 'Thuốc làm loãng máu và giảm đau', '#34495e', 'pill', 8, true, 'Thuốc chống đông máu', 'Viên nén'),
('cetirizine', 'Cetirizine', 'Thuốc kháng histamine điều trị dị ứng', '#8e44ad', 'pill', 9, true, 'Thuốc chống dị ứng', 'Viên nén'),
('morphine', 'Morphine', 'Thuốc giảm đau mạnh', '#c0392b', 'syringe', 10, true, 'Thuốc giảm đau mạnh', 'Tiêm')
ON CONFLICT (code) DO UPDATE SET
    name_vi = EXCLUDED.name_vi,
    description_vi = EXCLUDED.description_vi,
    color_code = EXCLUDED.color_code,
    icon_name = EXCLUDED.icon_name,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active,
    drug_class = EXCLUDED.drug_class,
    dosage_form = EXCLUDED.dosage_form,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- 6. STATUS_VALUES TABLE - Trạng thái
-- =====================================================
INSERT INTO status_values (code, name_vi, description_vi, color_code, icon_name, sort_order, is_active, applies_to) VALUES
('hoat_dong', 'Hoạt động', 'Hiện đang hoạt động và có sẵn', '#27ae60', 'check-circle', 1, true, 'chung'),
('khong_hoat_dong', 'Không hoạt động', 'Hiện không hoạt động hoặc không có sẵn', '#95a5a6', 'x-circle', 2, true, 'chung'),
('dang_cho', 'Đang chờ', 'Đang chờ phê duyệt hoặc xử lý', '#f39c12', 'clock', 3, true, 'chung'),
('hoan_thanh', 'Hoàn thành', 'Đã hoàn thành thành công', '#27ae60', 'check', 4, true, 'cuoc_hen'),
('da_huy', 'Đã hủy', 'Đã hủy bỏ hoặc chấm dứt', '#e74c3c', 'x', 5, true, 'cuoc_hen'),
('da_len_lich', 'Đã lên lịch', 'Đã lên lịch cho ngày tương lai', '#3498db', 'calendar', 6, true, 'cuoc_hen'),
('dang_tien_hanh', 'Đang tiến hành', 'Hiện đang được xử lý', '#9b59b6', 'play', 7, true, 'cuoc_hen'),
('dang_nghi_phep', 'Đang nghỉ phép', 'Tạm thời vắng mặt hoặc nghỉ phép', '#e67e22', 'pause', 8, true, 'bac_si'),
('da_xac_nhan', 'Đã xác nhận', 'Đã xác nhận và kiểm tra', '#16a085', 'shield-check', 9, true, 'cuoc_hen'),
('khong_den', 'Không đến', 'Không đến cuộc hẹn', '#c0392b', 'user-x', 10, true, 'cuoc_hen')
ON CONFLICT (code) DO UPDATE SET
    name_vi = EXCLUDED.name_vi,
    description_vi = EXCLUDED.description_vi,
    color_code = EXCLUDED.color_code,
    icon_name = EXCLUDED.icon_name,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active,
    applies_to = EXCLUDED.applies_to,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- 7. PAYMENT_METHODS TABLE - Phương thức thanh toán
-- =====================================================
INSERT INTO payment_methods (code, name_vi, description_vi, color_code, icon_name, sort_order, is_active, requires_verification, processing_fee) VALUES
('tien_mat', 'Tiền mặt', 'Thanh toán bằng tiền mặt', '#27ae60', 'banknote', 1, true, false, 0.00),
('the_tin_dung', 'Thẻ tín dụng', 'Thanh toán bằng thẻ tín dụng', '#3498db', 'credit-card', 2, true, true, 2.50),
('the_ghi_no', 'Thẻ ghi nợ', 'Thanh toán bằng thẻ ghi nợ', '#2980b9', 'credit-card', 3, true, true, 1.50),
('chuyen_khoan', 'Chuyển khoản ngân hàng', 'Chuyển khoản điện tử', '#8e44ad', 'building-bank', 4, true, true, 5.00),
('bao_hiem', 'Bảo hiểm', 'Thanh toán qua bảo hiểm', '#e74c3c', 'shield', 5, true, true, 0.00),
('vi_dien_tu', 'Ví điện tử', 'Thanh toán qua ví điện tử', '#f39c12', 'smartphone', 6, true, true, 1.00),
('sec', 'Séc', 'Thanh toán bằng séc', '#95a5a6', 'file-text', 7, true, true, 3.00),
('tra_gop', 'Trả góp', 'Kế hoạch thanh toán trả góp', '#e67e22', 'calendar-days', 8, true, true, 10.00)
ON CONFLICT (code) DO UPDATE SET
    name_vi = EXCLUDED.name_vi,
    description_vi = EXCLUDED.description_vi,
    color_code = EXCLUDED.color_code,
    icon_name = EXCLUDED.icon_name,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active,
    requires_verification = EXCLUDED.requires_verification,
    processing_fee = EXCLUDED.processing_fee,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- THÔNG BÁO HOÀN THÀNH
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '✅ Đã cập nhật tất cả bảng enum với dữ liệu tiếng Việt!';
    RAISE NOTICE '📊 Các bảng đã cập nhật: specialties, room_types, diagnosis, medications, status_values, payment_methods';
    RAISE NOTICE '🏥 Lưu ý: Departments sử dụng bảng departments riêng, chạy populate-departments-vietnamese.sql';
    RAISE NOTICE '🔄 Bây giờ bạn có thể test frontend enum data fetching.';
END $$;
