# Hướng dẫn từng bước loại bỏ hệ thống Bilingual

## 🚨 Lỗi "relation does not exist" - <PERSON><PERSON><PERSON><PERSON> pháp

Lỗi này xảy ra vì các bảng enum chưa tồn tại trong database của bạn. Hãy làm theo các bước sau:

## 📋 Bước 1: Kiểm tra database hiện tại

1. **Mở Supabase Dashboard**
2. **Vào SQL Editor**
3. **Chạy script kiểm tra:**

```sql
-- Kiểm tra các bảng hiện có
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_type = 'BASE TABLE'
ORDER BY table_name;
```

4. **Kiểm tra cấu trúc bảng doctors:**

```sql
-- Kiểm tra cột trong bảng doctors
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'doctors' 
ORDER BY ordinal_position;
```

## 📋 Bước 2: Tạ<PERSON> các bảng enum (nếu chưa có)

Nếu bạn không thấy các bảng enum trong kết quả bước 1, hãy tạo chúng:

### 2.1. Copy và chạy script tạo bảng:

```sql
-- Tạo bảng departments_enum
CREATE TABLE IF NOT EXISTS departments_enum (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255),
    description TEXT,
    color_code VARCHAR(7),
    icon_name VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tạo bảng specialties
CREATE TABLE IF NOT EXISTS specialties (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255),
    description TEXT,
    color_code VARCHAR(7),
    icon_name VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tạo bảng room_types
CREATE TABLE IF NOT EXISTS room_types (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255),
    description TEXT,
    color_code VARCHAR(7),
    icon_name VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tạo bảng diagnosis
CREATE TABLE IF NOT EXISTS diagnosis (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255),
    description TEXT,
    color_code VARCHAR(7),
    icon_name VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    icd_code VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tạo bảng medications
CREATE TABLE IF NOT EXISTS medications (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255),
    description TEXT,
    color_code VARCHAR(7),
    icon_name VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    dosage_form VARCHAR(100),
    strength VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tạo bảng status_values
CREATE TABLE IF NOT EXISTS status_values (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255),
    description TEXT,
    color_code VARCHAR(7),
    icon_name VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    applies_to VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tạo bảng payment_methods
CREATE TABLE IF NOT EXISTS payment_methods (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255),
    description TEXT,
    color_code VARCHAR(7),
    icon_name VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    requires_verification BOOLEAN DEFAULT false,
    processing_fee DECIMAL(8,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 📋 Bước 3: Loại bỏ trường languages_spoken

```sql
-- Xóa cột languages_spoken khỏi bảng doctors (nếu có)
ALTER TABLE doctors DROP COLUMN IF EXISTS languages_spoken;
```

## 📋 Bước 4: Populate dữ liệu tiếng Việt

Copy và chạy nội dung từ file `backend/scripts/populate-enum-tables-vietnam-only.sql`

## 📋 Bước 5: Kiểm tra kết quả

```sql
-- Kiểm tra các bảng đã tạo
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
    'departments_enum', 'specialties', 'room_types', 
    'diagnosis', 'medications', 'status_values', 'payment_methods'
)
ORDER BY table_name;

-- Kiểm tra dữ liệu mẫu
SELECT code, name, description FROM departments_enum LIMIT 3;
SELECT code, name, description FROM specialties LIMIT 3;

-- Kiểm tra bảng doctors không còn languages_spoken
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'doctors' 
AND column_name = 'languages_spoken';
```

## ✅ Kết quả mong đợi

Sau khi hoàn thành:
- ✅ 7 bảng enum đã được tạo
- ✅ Tất cả bảng enum có dữ liệu tiếng Việt
- ✅ Bảng doctors không còn cột `languages_spoken`
- ✅ Hệ thống chuyển sang Vietnamese-only

## 🚨 Nếu vẫn gặp lỗi

1. **Kiểm tra quyền truy cập:** Đảm bảo bạn đang sử dụng Service Role Key
2. **Kiểm tra tên bảng:** Có thể database của bạn sử dụng tên bảng khác
3. **Chạy từng câu lệnh:** Thay vì chạy toàn bộ script, hãy chạy từng câu lệnh một

## 📞 Hỗ trợ thêm

Nếu vẫn gặp vấn đề, hãy:
1. Chụp ảnh màn hình kết quả của bước 1 (danh sách bảng hiện có)
2. Chụp ảnh màn hình lỗi cụ thể
3. Cho biết bạn đang ở bước nào
