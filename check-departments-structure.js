const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '../.env.local' });

const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);

async function checkStructure() {
  console.log('🔍 Checking departments table structure...\n');
  
  try {
    const { data, error } = await supabase
      .from('departments')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('❌ Error:', error.message);
      return;
    }
    
    if (data && data.length > 0) {
      console.log('✅ Found existing data. Table structure:');
      const sample = data[0];
      Object.keys(sample).forEach(key => {
        console.log(`   ${key}: ${typeof sample[key]} = ${sample[key]}`);
      });
    } else {
      console.log('⚠️  Table empty, checking with insert test...');
      
      const testData = {
        department_id: 'TEST001',
        name: 'Test Department',
        description: 'Test description',
        location: 'Test location',
        phone_number: '0123456789',
        email: '<EMAIL>',
        status: 'active',
        is_active: true
      };
      
      const { error: insertError } = await supabase
        .from('departments')
        .insert(testData);
      
      if (insertError) {
        console.log('💡 Insert error shows expected structure:');
        console.log(insertError.message);
      } else {
        console.log('✅ Test insert successful, cleaning up...');
        await supabase.from('departments').delete().eq('department_id', 'TEST001');
      }
    }
  } catch (err) {
    console.error('❌ Error:', err.message);
  }
}

checkStructure();
