-- =====================================================
-- POPULATE DEPARTMENTS TABLE - VIETNAMESE VERSION
-- =====================================================
-- Chèn các khoa/phòng ban phù hợp cho bệnh viện Việt Nam

-- Xóa dữ liệu cũ (tùy chọn)
-- DELETE FROM departments WHERE department_id LIKE 'DEPT%';

-- =====================================================
-- CHÈN CÁC KHOA/PHÒNG BAN CHÍNH
-- =====================================================

INSERT INTO departments (
    department_id, 
    name, 
    description, 
    location, 
    phone_number, 
    email, 
    status, 
    is_active,
    created_at,
    updated_at
) VALUES 
-- 1. Khoa Tim mạch
(
    'DEPT001', 
    'Khoa Tim mạch', 
    'Chuyên khoa điều trị các bệnh lý tim mạch, mạch máu và hệ tuần hoàn', 
    'Tầng 3, Tòa nhà A', 
    '0234567801', 
    '<EMAIL>', 
    'active', 
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),

-- 2. Khoa Thần kinh
(
    'DEPT002', 
    'Khoa Thần kinh', 
    'Chuyên khoa điều trị các bệnh lý não bộ, hệ thần kinh trung ương và ngoại biên', 
    'Tầng 4, Tòa nhà A', 
    '0234567802', 
    '<EMAIL>', 
    'active', 
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),

-- 3. Khoa Nhi
(
    'DEPT003', 
    'Khoa Nhi', 
    'Chuyên khoa chăm sóc và điều trị cho trẻ em từ sơ sinh đến 16 tuổi', 
    'Tầng 2, Tòa nhà B', 
    '0234567803', 
    '<EMAIL>', 
    'active', 
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),

-- 4. Khoa Sản phụ khoa
(
    'DEPT004', 
    'Khoa Sản phụ khoa', 
    'Chuyên khoa chăm sóc sức khỏe sinh sản phụ nữ, thai sản và phụ khoa', 
    'Tầng 3, Tòa nhà B', 
    '0234567804', 
    '<EMAIL>', 
    'active', 
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),

-- 5. Khoa Nội tổng hợp
(
    'DEPT005', 
    'Khoa Nội tổng hợp', 
    'Chuyên khoa điều trị các bệnh lý nội khoa tổng quát và bệnh mãn tính', 
    'Tầng 1, Tòa nhà A', 
    '0234567805', 
    '<EMAIL>', 
    'active', 
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),

-- 6. Khoa Ngoại tổng hợp
(
    'DEPT006', 
    'Khoa Ngoại tổng hợp', 
    'Chuyên khoa phẫu thuật tổng quát và các thủ thuật ngoại khoa', 
    'Tầng 2, Tòa nhà A', 
    '0234567806', 
    '<EMAIL>', 
    'active', 
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),

-- 7. Khoa Chấn thương chỉnh hình
(
    'DEPT007', 
    'Khoa Chấn thương chỉnh hình', 
    'Chuyên khoa điều trị chấn thương, gãy xương và các bệnh lý xương khớp', 
    'Tầng 1, Tòa nhà C', 
    '0234567807', 
    '<EMAIL>', 
    'active', 
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),

-- 8. Khoa Cấp cứu
(
    'DEPT008', 
    'Khoa Cấp cứu', 
    'Khoa cấp cứu 24/7, xử lý các trường hợp khẩn cấp và cấp cứu', 
    'Tầng trệt, Tòa nhà chính', 
    '0234567808', 
    '<EMAIL>', 
    'active', 
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),

-- 9. Khoa Mắt
(
    'DEPT009', 
    'Khoa Mắt', 
    'Chuyên khoa điều trị các bệnh lý về mắt và thị lực', 
    'Tầng 2, Tòa nhà C', 
    '0234567809', 
    '<EMAIL>', 
    'active', 
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),

-- 10. Khoa Tai mũi họng
(
    'DEPT010', 
    'Khoa Tai mũi họng', 
    'Chuyên khoa điều trị các bệnh lý tai, mũi, họng và đầu cổ', 
    'Tầng 3, Tòa nhà C', 
    '0234567810', 
    '<EMAIL>', 
    'active', 
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),

-- 11. Khoa Da liễu
(
    'DEPT011', 
    'Khoa Da liễu', 
    'Chuyên khoa điều trị các bệnh lý da, tóc, móng và bệnh xã hội', 
    'Tầng 1, Tòa nhà D', 
    '0234567811', 
    '<EMAIL>', 
    'active', 
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),

-- 12. Khoa Hồi sức cấp cứu
(
    'DEPT012', 
    'Khoa Hồi sức cấp cứu', 
    'Khoa hồi sức tích cực, chăm sóc bệnh nhân nặng và nguy kịch', 
    'Tầng 5, Tòa nhà A', 
    '0234567812', 
    '<EMAIL>', 
    'active', 
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
)

ON CONFLICT (department_id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    location = EXCLUDED.location,
    phone_number = EXCLUDED.phone_number,
    email = EXCLUDED.email,
    status = EXCLUDED.status,
    is_active = EXCLUDED.is_active,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- THÔNG BÁO HOÀN THÀNH
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '✅ Đã cập nhật bảng departments với 12 khoa/phòng ban!';
    RAISE NOTICE '🏥 Các khoa đã thêm: Tim mạch, Thần kinh, Nhi, Sản phụ khoa, Nội TH, Ngoại TH, Chấn thương, Cấp cứu, Mắt, TMH, Da liễu, Hồi sức';
    RAISE NOTICE '📞 Mỗi khoa đều có thông tin liên lạc và vị trí đầy đủ';
    RAISE NOTICE '🔄 Bây giờ bạn có thể sử dụng bảng departments thay vì departments_enum';
END $$;
